<div class="container-fluid p-4">
    <!-- Header -->
    <header class="d-flex justify-content-between align-items-center mb-4 p-3 bg-white rounded-3 shadow-sm">
        <div>
            <h1 class="main-header mb-1">Analytics</h1>
        </div>
        <div class="header-actions d-flex align-items-center gap-2">
            <!-- <PERSON><PERSON> - Exact same as old analytics -->
            <button class="btn btn-light bg-white border" wire:click="toggleFilterPopup">
                <svg width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M1.55393 0.520138C0.941545 0.646511 0.462285 1.0355 0.186151 1.63027C-0.0847294 2.21375 -0.0583501 2.91718 0.254644 3.45577C0.30836 3.54823 2.37076 6.14256 4.83774 9.22094L9.32315 14.818L9.3318 16.9354C9.34124 19.2523 9.33396 19.1445 9.50417 19.4878C9.65484 19.7918 9.74484 19.87 11.4293 21.1609C12.9172 22.3011 13.2933 22.5816 13.2933 22.5512C13.2933 22.5455 13.2451 22.4342 13.1862 22.304C12.5191 20.8292 12.2928 18.9918 12.5704 17.3061C13.1352 13.8766 15.6214 11.1371 18.9484 10.2779L19.2812 10.1919L21.9253 6.89353C24.3095 3.91944 24.5812 3.5704 24.6894 3.34356C24.8516 3.00366 24.9074 2.72984 24.8883 2.3678C24.8609 1.84548 24.6815 1.43402 24.3209 1.06563C24.0852 0.82482 23.8537 0.677259 23.5412 0.568551L23.3223 0.492409L12.5271 0.487936C3.70904 0.484275 1.69921 0.490173 1.55393 0.520138ZM20.3853 11.6344C19.8343 11.6845 19.117 11.8473 18.6117 12.0371C18.221 12.1839 17.5104 12.5493 17.1856 12.7705C16.538 13.2117 15.8605 13.8543 15.4374 14.4287C14.691 15.4419 14.2357 16.5776 14.0544 17.8789C14.0081 18.2109 14.0089 19.3091 14.0556 19.6539C14.269 21.2282 14.9323 22.6361 15.9699 23.717C16.5948 24.3679 17.1418 24.7779 17.9107 25.1715C19.5362 26.0037 21.4475 26.1415 23.1992 25.5531C25.0665 24.9257 26.5979 23.5131 27.4126 21.6664C27.6623 21.1005 27.8663 20.353 27.9656 19.6399C28.0115 19.311 28.0115 18.2077 27.9656 17.8789C27.7846 16.5796 27.3272 15.4386 26.5827 14.4289C26.1617 13.858 25.4827 13.2102 24.8618 12.7871C24.5143 12.5503 23.8127 12.1881 23.4083 12.0368C22.486 11.6915 21.3788 11.5442 20.3853 11.6344ZM18.5293 15.602C18.1591 15.6765 17.9078 15.9925 17.9078 16.3834C17.9078 16.7132 17.9253 16.7368 18.965 17.8017L19.9 18.7594L18.9678 19.7167C17.93 20.7827 17.9061 20.8146 17.9072 21.1351C17.9082 21.4522 18.0621 21.7073 18.342 21.8559C18.4511 21.9138 18.5176 21.9285 18.6704 21.9286C19.0084 21.9289 19.0222 21.9182 20.0725 20.8484L21.01 19.8933L21.9476 20.8484C22.9978 21.9182 23.0116 21.9289 23.3497 21.9286C23.5024 21.9285 23.569 21.9138 23.6781 21.8559C23.9579 21.7073 24.1118 21.4522 24.1129 21.1351C24.1139 20.8155 24.0895 20.7827 23.0521 19.7166L22.1204 18.759L23.0552 17.8015C24.0947 16.7368 24.1123 16.7132 24.1123 16.3834C24.1123 15.8596 23.6524 15.4868 23.1638 15.6146C23.0748 15.6379 22.9632 15.684 22.9158 15.7171C22.8685 15.7501 22.4203 16.1932 21.9199 16.7016L21.01 17.6262L20.1002 16.7016C19.5997 16.1932 19.1533 15.7518 19.1082 15.7208C18.9541 15.6149 18.7117 15.5652 18.5293 15.602Z"
                        fill="black" />
                </svg>
            </button>

            <!-- Refresh Button - Exact same as old analytics -->
            <button class="btn btn-light bg-white border" wire:click="resetFilters">
                <svg width="26" height="27" viewBox="0 0 26 27" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M0.0336782 14.713C0.110992 15.5135 0.174489 15.9943 0.258356 16.4141C0.366961 16.9578 0.651173 18.018 0.70319 18.0735C0.728639 18.1007 1.74119 17.7755 2.57742 17.4716C2.6244 17.4546 2.62704 17.4133 2.58783 17.3086C2.36158 16.7047 2.10637 15.2912 2.05176 14.3393L2.01697 13.7336L1.00848 13.7336L-5.67163e-07 13.7336L0.00812705 14.1331C0.0125972 14.3528 0.0240775 14.6138 0.0336782 14.713ZM1.91202 20.7183C2.36015 21.4586 3.0254 22.3181 3.67855 23.0007C4.01158 23.3488 4.30357 23.627 4.32739 23.6189C4.35127 23.6109 4.66418 23.2687 5.02281 22.8586L5.6749 22.1129L5.2291 21.6605C4.56335 20.9849 3.98247 20.2153 3.42553 19.2709C3.37641 19.1875 3.29168 19.2232 2.49848 19.6609C2.01834 19.9259 1.62679 20.1678 1.62831 20.1985C1.62989 20.2293 1.75754 20.4632 1.91202 20.7183ZM5.89251 8.78506L9.95632 8.78506L9.95632 7.75411L9.95632 6.72316L7.36219 6.72316L4.76806 6.72316L4.87646 6.56862C5.08351 6.27356 6.06543 5.27039 6.47308 4.93744C8.47404 3.30317 10.8547 2.50697 13.4105 2.61728C16.3137 2.74264 18.7978 3.82942 20.781 5.84195C22.4676 7.55343 23.4847 9.61652 23.8582 12.0841C23.982 12.9016 23.982 14.5657 23.8582 15.3832C23.3016 19.06 21.1797 22.0527 17.9689 23.6894C16.5371 24.4193 15.0656 24.7857 13.3008 24.8517L12.4545 24.8833L12.4241 25.4813C12.4073 25.8102 12.3812 26.2707 12.3661 26.5046L12.3385 26.9298L12.9888 26.9297C16.608 26.9287 19.9824 25.4885 22.4244 22.9024C24.8504 20.3333 26.1171 16.9126 25.9915 13.2697C25.8723 9.81411 24.5816 6.76501 22.2265 4.37511C20.3457 2.46655 18.0788 1.25967 15.4489 0.766771C15.0708 0.695894 14.5595 0.623365 14.3128 0.605581C14.066 0.587848 13.8522 0.553623 13.8376 0.529601C13.8014 0.47027 12.4269 0.473876 12.1973 0.533929C12.0963 0.560323 11.7622 0.607849 11.4548 0.639551C10.1942 0.769399 8.7643 1.20271 7.51981 1.8319C6.296 2.45063 5.28401 3.18286 4.31723 4.14912L3.86061 4.60547L3.86061 2.57145L3.86061 0.537435L2.84466 0.537435L1.82871 0.537435L1.82871 4.66125L1.82871 8.78506L5.89251 8.78506ZM6.36239 25.1404C6.80631 25.4027 7.66037 25.8154 8.21381 26.035C8.72356 26.2372 9.9805 26.6206 10.134 26.6206C10.1663 26.6206 10.22 26.4756 10.2533 26.2984C10.2867 26.1212 10.3787 25.6722 10.4579 25.3006L10.6019 24.6249L10.0102 24.4607C9.27162 24.2555 8.29432 23.8602 7.69583 23.5245C7.44438 23.3834 7.21914 23.2626 7.19532 23.2561C7.14701 23.2428 6.14874 24.8594 6.14736 24.9532C6.14691 24.986 6.24363 25.0702 6.36239 25.1404ZM13.1058 26.9091C13.5388 26.9091 13.7159 26.9012 13.4994 26.8915C13.2829 26.8818 12.9286 26.8818 12.7121 26.8915C12.4956 26.9012 12.6727 26.9091 13.1058 26.9091Z"
                        fill="black" />
                </svg>
            </button>

            <!-- Analytics Date Filter Dropdown - New Classes -->
            <div class="analytics-date-dropdown">
                <button class="analytics-date-btn" onclick="AnalyticsDateFilter.toggle()" type="button">
                    {{ $selectedDateFilter ?: 'Choose Date' }}
                    <svg class="analytics-dropdown-arrow" width="12" height="8" viewBox="0 0 12 8"
                        fill="none">
                        <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </button>
                <div class="analytics-date-menu" id="analyticsDateOptions">
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '24 Hours')">
                        Last 24 Hours
                    </div>
                    <div class="analytics-date-divider"></div>
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '7 Days')">
                        Last 7 Days
                    </div>
                    <div class="analytics-date-divider"></div>
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '30 Days')">
                        Last 30 Days
                    </div>
                    <div class="analytics-date-divider"></div>
                    <div class="analytics-date-item" wire:click="$set('selectedDateFilter', '60 Days')">
                        Last 60 Days
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Analytics Filter Modal - New Classes -->
    @if ($showFilterPopup)
        <div class="analytics-filter-overlay" wire:click="toggleFilterPopup">
            <div class="analytics-filter-modal" wire:click.stop>
                <div class="analytics-filter-header">
                    <h3 class="analytics-filter-title">Filter</h3>
                    <button type="button" class="analytics-filter-close" wire:click="toggleFilterPopup">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </button>
                </div>

                <!-- Analytics Type Filter -->
                <div class="analytics-filter-section">
                    <h4 class="analytics-filter-section-title">Type</h4>
                    <div class="analytics-filter-options">
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Voice"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/voice_ay.png') }}" alt="Voice" width="15" height="15"
                                    class="analytics-filter-icon">
                                Voice
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Non-Voice"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <i class="fas fa-comment analytics-filter-icon" style="color: #28a745;"></i>
                                Non-Voice
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Analytics Social Media Filter -->
                <div class="analytics-filter-section">
                    <h4 class="analytics-filter-section-title">Social Media</h4>
                    <div class="analytics-filter-grid">
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Facebook"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/<EMAIL>') }}" alt="Facebook" width="15"
                                    height="15" class="analytics-filter-icon">
                                Facebook
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Whatsapp"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/whatsapp.png') }}" alt="WhatsApp" width="15"
                                    height="15" class="analytics-filter-icon">
                                WhatsApp
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Email"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/mail.png') }}" alt="Email" width="15" height="15"
                                    class="analytics-filter-icon">
                                Email
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Instagram"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/instagram_color.png') }}" alt="Instagram" width="15"
                                    height="15" class="analytics-filter-icon">
                                Instagram
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Twitter"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/twitter_color.png') }}" alt="Twitter" width="15"
                                    height="15" class="analytics-filter-icon">
                                Twitter
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Web Chat"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <img src="{{ asset('img/web_chat_color.png') }}" alt="Web Chat" width="15"
                                    height="15" class="analytics-filter-icon">
                                Web Chat
                            </span>
                        </label>
                        <label class="analytics-filter-option">
                            <input type="radio" wire:model="selectedChannel" value="Google"
                                class="analytics-filter-radio">
                            <span class="analytics-filter-label">
                                <i class="fa-brands fa-google analytics-filter-icon" style="color: #02a34e;"></i>
                                Google
                            </span>
                        </label>
                    </div>
                </div>
                <!-- Analytics Date Range Filter -->
                <div class="analytics-filter-section">
                    <h4 class="analytics-filter-section-title">Date Range</h4>
                    <div class="analytics-date-range">
                        <div class="analytics-date-input">
                            <label class="analytics-date-label">From Date</label>
                            <input type="date" class="analytics-date-field" wire:model="fromDate">
                        </div>
                        <div class="analytics-date-input">
                            <label class="analytics-date-label">To Date</label>
                            <input type="date" class="analytics-date-field" wire:model="toDate">
                        </div>
                    </div>
                </div>

                <!-- Analytics Filter Actions -->
                <div class="analytics-filter-actions">
                    <button type="button" class="analytics-filter-reset" wire:click="resetFilters">
                        Reset
                    </button>
                    <button type="button" class="analytics-filter-apply" wire:click="applyFilters">
                        Apply Filters
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Analytics Filter CSS - New Classes to Avoid Conflicts -->
    <style>
        /* Analytics Date Dropdown */
        .analytics-date-dropdown {
            position: relative;
            display: inline-block;
        }

        .analytics-date-btn {
            background: white;
            border: 1px solid #e0e0e0;
            padding: 11px 13px;
            height: 44px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #6c757d;
            transition: all 0.2s;
        }

        .analytics-date-btn:hover {
            border-color: #00a34e;
            color: #00a34e;
        }

        .analytics-dropdown-arrow {
            transition: transform 0.2s;
        }

        .analytics-date-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            padding: 8px;
            display: none;
        }

        .analytics-date-menu.show {
            display: block;
        }

        .analytics-date-item {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .analytics-date-item:hover {
            background-color: #f8f9fa;
        }

        .analytics-date-divider {
            height: 1px;
            background-color: #e9ecef;
            margin: 4px 0;
        }

        /* Analytics Filter Modal */
        .analytics-filter-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1050;
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            padding: 80px 50px 20px 20px;
        }

        .analytics-filter-modal {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 400px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
        }

        .analytics-filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 0 20px;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .analytics-filter-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #333;
        }

        .analytics-filter-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .analytics-filter-close:hover {
            background-color: #f8f9fa;
        }

        .analytics-filter-section {
            padding: 0 20px 20px 20px;
        }

        .analytics-filter-section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .analytics-filter-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .analytics-filter-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .analytics-filter-option {
            display: block;
            cursor: pointer;
            margin: 0;
        }

        .analytics-filter-radio {
            display: none;
        }

        .analytics-filter-label {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.2s;
            font-size: 14px;
            justify-content: center;
        }

        .analytics-filter-label:hover {
            background: #e9ecef;
            border-color: #00a34e;
        }

        .analytics-filter-radio:checked+.analytics-filter-label {
            background: #e8f5e8;
            border-color: #00a34e;
            color: #00a34e;
            font-weight: 600;
        }

        .analytics-filter-icon {
            flex-shrink: 0;
        }

        /* Date Range */
        .analytics-date-range {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .analytics-date-input {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .analytics-date-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .analytics-date-field {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .analytics-date-field:focus {
            outline: none;
            border-color: #00a34e;
            box-shadow: 0 0 0 2px rgba(0, 163, 78, 0.1);
        }

        /* Filter Actions */
        .analytics-filter-actions {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        .analytics-filter-reset {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .analytics-filter-reset:hover {
            background: #5a6268;
        }

        .analytics-filter-apply {
            background: #00a34e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .analytics-filter-apply:hover {
            background: #008a42;
        }
    </style>

    <!-- First Row -->
    <div class="row g-3 mb-3">
        <!-- Received Trend Chart - Suitable Width -->
        <div class="col-lg-6 d-flex align-items-stretch">
            <div class="card shadow rounded border w-100" style="border-radius: 1em; border: 1px solid #d6d6d6">
                <div class="card-body d-flex flex-column" style="padding: 2.5vh 1.8vw">
                    <div class="chart-header" style="margin-bottom: 20px">
                        <h4 style="margin: 0; font-size: 1.25rem; font-weight: 600">
                            Received Trend
                        </h4>

                        <hr class="head-seperator" style="margin: 20px 0 0 0; border-color: #d6d6d6" />
                    </div>
                    <div class="flex-grow-1" style="position: relative">
                        <div style="width: 100%; height: 100%">
                            <canvas id="RecivedticketsChart"></canvas>
                        </div>
                    </div>
                    <div class="chart-legend"
                        style="
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  margin-top: 12px;
                ">
                        <div
                            style="
                    width: 9px;
                    height: 9px;
                    background-color: #01a44f;
                    border-radius: 50%;
                  ">
                        </div>
                        <span style="font-size: 14px; color: #8f9090">Received</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Two Card Column -->
        <div class="col-lg-3 d-flex flex-column m-0" style="justify-content: space-between">
            <!-- SLA Performance Circles Card -->
            <div class="card p-2 mb-0 flex-grow-1" style="min-height: 0">
                <div class="card-body d-flex align-items-center justify-content-center h-100" style="padding: 8px">
                    <div class="row w-100 align-items-center justify-content-center text-center">
                        <div class="col-4" style="position: relative">
                            <div
                                style="
                      height: 70px;
                      width: 70px;
                      margin: 0 auto;
                      position: relative;
                    ">
                                <canvas id="resolutionSlaChart"></canvas>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 11px; font-weight: 400; line-height: 1.1">
                                Resolution SLA<br />Performance %
                            </h6>
                            <div
                                style="
                      position: absolute;
                      right: 0;
                      top: 15%;
                      height: 70%;
                      width: 1px;
                      background: #d6d6d6;
                    ">
                            </div>
                        </div>
                        <div class="col-4" style="position: relative">
                            <div
                                style="
                      height: 70px;
                      width: 70px;
                      margin: 0 auto;
                      position: relative;
                    ">
                                <canvas id="responseSlaChart"></canvas>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 11px; font-weight: 400; line-height: 1.1">
                                Response SLA<br />Performance %
                            </h6>
                            <div
                                style="
                      position: absolute;
                      right: 0;
                      top: 15%;
                      height: 70%;
                      width: 1px;
                      background: #d6d6d6;
                    ">
                            </div>
                        </div>
                        <div class="col-4" style="position: relative">
                            <div
                                style="
                      height: 70px;
                      width: 70px;
                      margin: 0 auto;
                      position: relative;
                    ">
                                <canvas id="resolutionSlaChart2"></canvas>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 11px; font-weight: 400; line-height: 1.1">
                                Resolution SLA %
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Gauge Charts Card -->
            <div class="card p-2 flex-grow-1 mt-2" style="min-height: 0">
                <div class="card-body d-flex align-items-center justify-content-center h-100" style="padding: 8px">
                    <div class="row w-100 align-items-center text-center" style="position: relative">
                        <div class="col-6" style="position: relative">
                            <div class="gauge-chart-container"
                                style="
                      width: 130px;
                      height: 65px;
                      margin: 0 auto;
                      position: relative;
                    ">
                                <canvas id="avgResponseTimeChart"></canvas>
                                <div
                                    style="
                        position: absolute;
                        left: 50%;
                        top: 80%;
                        transform: translate(-50%, -50%);
                        width: 100%;
                        text-align: center;
                        pointer-events: none;
                      ">
                                    <span
                                        style="
                          font-size: 12px;
                          font-weight: bold;
                          color: #000000;
                          border-radius: 6px;
                          padding: 1px 4px;
                        ">33d,05:10:40</span>
                                </div>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 10px; line-height: 1.1">
                                Average<br />Response Time
                            </h6>
                            <div
                                style="
                      position: absolute;
                      right: 0;
                      top: 10%;
                      height: 80%;
                      width: 1px;
                      background: #d6d6d6;
                    ">
                            </div>
                        </div>
                        <div class="col-6" style="position: relative">
                            <div class="gauge-chart-container"
                                style="
                      width: 130px;
                      height: 65px;
                      margin: 0 auto;
                      position: relative;
                    ">
                                <canvas id="ticketResolutionTimeChart"></canvas>
                                <div
                                    style="
                        position: absolute;
                        left: 50%;
                        top: 80%;
                        transform: translate(-50%, -50%);
                        width: 100%;
                        text-align: center;
                        pointer-events: none;
                      ">
                                    <span
                                        style="
                          font-size: 12px;
                          font-weight: bold;
                          color: #000000;
                          border-radius: 6px;
                          padding: 1px 4px;
                        ">33d,05:10:40</span>
                                </div>
                            </div>
                            <h6 class="mt-1 small" style="font-size: 10px; line-height: 1.1">
                                Aggregate<br />Ticket Resolution Time
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Numbers Chart - Made Narrower -->
        <div class="col-lg-3">
            <div class="card p-2" style="padding: 0.4rem !important">
                <div class="card-body p-2" style="padding: 0.4rem !important">
                    <div class="d-flex justify-content-between align-items-start">
                        <h5 class="chart-card-header mb-4" style="font-size: 0.9rem">
                            Total Numbers Of Received Tickets
                        </h5>
                        <span class="badge bg-light text-success"
                            style="font-size: 1rem; font-weight: 500; padding: 4px 10px">25</span>
                    </div>
                    <div class="progress-label">
                        <span style="font-size: 13px">New Tickets</span>
                        <strong>50%</strong>
                    </div>
                    <div class="progress mb-1">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 50%"
                            aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="progress-label">
                        <span style="font-size: 13px">Re-open Tickets</span>
                        <strong>25%</strong>
                    </div>
                    <div class="progress mb-1">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 25%"
                            aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="progress-label">
                        <span style="font-size: 13px">Resolved Tickets</span>
                        <strong>30%</strong>
                    </div>
                    <div class="progress mb-1">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 30%"
                            aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="progress-label">
                        <span style="font-size: 13px">In Progress Tickets</span>
                        <strong>70%</strong>
                    </div>
                    <div class="progress mb-1">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 70%"
                            aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="progress-label">
                        <span style="font-size: 13px">Pending Tickets</span>
                        <strong>70%</strong>
                    </div>
                    <div class="progress mb-1">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 70%"
                            aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="progress-label">
                        <span style="font-size: 13px">Closed Tickets</span>
                        <strong>70%</strong>
                    </div>
                    <div class="progress mb-1">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 70%"
                            aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="progress-label">
                        <span style="font-size: 13px">Number Of Current Active Tickets</span>
                        <strong>70%</strong>
                    </div>
                    <div class="progress mb-1">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 70%"
                            aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row -->
    <div class="row g-3">
        <!-- SLA Violation Charts -->
        <div class="col-lg-6">
            <div class="card p-0 h-100">
                <div class="card-body p-0 d-flex flex-column justify-content-around">
                    <div class="sla-item">
                        <p>Number Of SLA - Response Violated Tickets</p>
                        <div class="chart-container" style="width: 168px; height: 54px">
                            <canvas id="slaResponseChart"></canvas>
                        </div>
                        <span class="stat-value">67</span>
                    </div>
                    <hr class="my-0" style="border-color: #d6d6d6" />
                    <div class="sla-item">
                        <p>Number Of SLA - Resolution Violated Tickets</p>
                        <div class="chart-container" style="width: 168px; height: 61px">
                            <canvas id="slaResolutionChart"></canvas>
                        </div>
                        <span class="stat-value text-danger">41</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top 5 Ticket Types -->
        <div class="col-lg-6">
            <div class="ticket-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="mb-0" style="font-size: 1.2rem">Top 5 Ticket Types</h4>
                    <button class="expand-btn" style="font-size: 0.9rem; padding: 0.5rem 1rem"
                        onclick="expandView()">
                        Expand
                        <i class="fas fa-chevron-right" style="font-size: 0.8rem"></i>
                    </button>
                </div>

                <div class="flow-container" id="flowContainer" style="padding: 1.5vh 2vw">
                    <div class="main-node flow-node" style="font-size: 1rem">
                        Top 10 Tickets
                    </div>
                    <svg class="connection-svg" id="connectionSvg" style="width: 100%; height: auto"></svg>
                    <div class="ticket-list" id="ticketList" style="font-size: 0.9rem"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter CSS - Exact same as old analytics -->


<!-- Analytics Filter JavaScript - New Functions to Avoid Conflicts -->
<script>
    // Analytics Date Filter - Namespaced to avoid conflicts
    window.AnalyticsDateFilter = {
        toggle: function() {
            const menu = document.getElementById('analyticsDateOptions');
            if (menu) {
                menu.classList.toggle('show');
            }
        },

        close: function() {
            const menu = document.getElementById('analyticsDateOptions');
            if (menu) {
                menu.classList.remove('show');
            }
        },

        init: function() {
            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                const dropdown = document.querySelector('.analytics-date-dropdown');
                const menu = document.getElementById('analyticsDateOptions');

                if (dropdown && menu && !dropdown.contains(event.target)) {
                    menu.classList.remove('show');
                }
            });

            // Close dropdown when pressing Escape
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    window.AnalyticsDateFilter.close();
                }
            });
        }
    };

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        window.AnalyticsDateFilter.init();
    });

    // Listen for Livewire events to update chart
    window.addEventListener('refreshReceivedTrendChart', function(event) {
        const newData = event.detail;
        console.log('Received chart data:', newData);

        if (window.AnalyticsCharts && window.AnalyticsCharts.updateReceivedTrend) {
            window.AnalyticsCharts.updateReceivedTrend(newData);
        }
    });
</script>
