<?php

namespace App\Http\Controllers\Tenant\Supervisor\Analystics;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Resource;
use App\Models\Tenant\ResourceAction;
use App\Models\Tenant\ResourceInfo;
use App\Models\Tenant\Ticket;
use App\Models\Tenant\TicketAction;
use App\Models\Tenant\User;
use App\Models\User as ModelsUser;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

use function Sodium\compare;

class AnalysticsController extends Controller
{
    public $updatedData;
    public $channel;

    public function __invokeOLD()
    {
        return view('tenant.supervisor.analytics.index');
    }

    public function __invoke()
    {
        if (!auth()->user()->can(['analytics'])) {
            return redirect()->route('supervisor.no-permission');
        }
        $dateType = 'Choose Date';
        $filterType = 'voice';
        $social = 'All';
        $fromDateTime = '';
        $toDateTime = '';
        $aggregateTicketResolutionTime = 0;
        $received_tickets = 0;
        $new_tickets = 0;
        $in_progress_tickets = 0;
        $reopen_tickets = 0;
        $pending_tickets = 0;
        $resolved_tickets = 0;
        $closed_tickets = 0;
        $filteredData = [
            "" => 0,
            "" => 0
        ];
        
        $ReceivedLabels = array_keys($filteredData);
        $ReceivedData = array_values($filteredData);

        // echo $totalMinutes;
        $aggregateTicketResolutionTime = 0;
        return view('tenant.supervisor.analytics.index', [
            'dateType' =>   $dateType,
            'filterType' =>   $filterType,
            'social' =>   $social,
            'fromDateTime' =>   $fromDateTime,
            'toDateTime' =>   $toDateTime,
            'avgAggregateTicketResolutionTime' =>   $aggregateTicketResolutionTime,
            'received_tickets' =>   $received_tickets,
            'new_tickets' =>   $new_tickets,
            'in_progress_tickets' =>   $in_progress_tickets,
            'reopen_tickets' =>   $reopen_tickets,
            'pending_tickets' =>   $pending_tickets,
            'resolved_tickets' =>   $resolved_tickets,
            'closed_tickets' =>   $closed_tickets,

        ], compact('ReceivedLabels', 'ReceivedData'));
    }

    public function handleSelectChange(Request $request)
    {
        $dateType = 'Choose Date';
        $social = $request->input('social');
        $filterType = $request->input('type');
        $mySelect = $request->input('mySelect2');
        if($mySelect){
            $dateType = 'Last '.$mySelect;
        }
    
        $From_date = $request->input('From_date');
        $To_date = $request->input('To_date');
        $avgAggregateTicketResolutionTime = 0;
        $received_tickets = 0;
        $new_tickets = 0;
        $in_progress_tickets = 0;
        $reopen_tickets = 0;
        $pending_tickets = 0;
        $resolved_tickets = 0;
        $closed_tickets = 0;


        $fromDateTime = new DateTime();
        $toDateTime = new DateTime();

        if ($From_date != null && $To_date != null) {

            $fromDateTime = new DateTime($From_date);
            $toDateTime = new DateTime($To_date);
        } else {
            switch ($mySelect) {
                case '24 Hours':
                    $fromDateTime = Carbon::now()->subHours(24);
                    break;
                case '7 Days':
                    $fromDateTime = Carbon::now()->subDays(7);
                    break;
                case '30 Days':
                    $fromDateTime = Carbon::now()->subDays(30);
                    break;
                case '60 Days':
                    $fromDateTime = Carbon::now()->subDays(60);
                    break;
                default:
                    $fromDateTime = Carbon::now()->subDays(60);
                    break;
            }
        }

        if($filterType != 'voice'){

            $ticketsQuery = Resource::where(function ($query) {
                $query->where('status', 'Closed')
                      ->orWhere('status', 'Resolved');
            })
            ->where('created_at', '>=', $fromDateTime)
            ->where('created_at', '<=', $toDateTime);
        
            $tickets = $ticketsQuery->get();
        }else{

            $tickets = Ticket::where(function ($query) {
                $query->where('status', 'Closed')
                    ->orWhere('status', 'Resolved');
            })
                ->where('created_at', '>=', $fromDateTime)
                ->where('created_at', '<=', $toDateTime)
                ->get();

        }

        $ticketCount = $tickets->count();


        if ($ticketCount > 0) {
            $totalResolutionTimeInSeconds = $tickets->sum(function ($ticket) {
                return $ticket->updated_at->diffInSeconds($ticket->created_at);
            });

            $averageResolutionTimeInSeconds = $totalResolutionTimeInSeconds / $ticketCount;

            $days = floor($averageResolutionTimeInSeconds / 86400); // 86400 seconds in a day
            $hours = floor(($averageResolutionTimeInSeconds % 86400) / 3600);
            $minutes = floor(($averageResolutionTimeInSeconds % 3600) / 60);
            $seconds = $averageResolutionTimeInSeconds % 60;

            $avgAggregateTicketResolutionTime = '';
            if ($days > 0) {
                $avgAggregateTicketResolutionTime .= $days . 'd,';
            }
            $avgAggregateTicketResolutionTime .= sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        } else {
            $avgAggregateTicketResolutionTime = 0;
        }

        if($filterType == 'voice'){
            $all_tickets = Ticket::where('created_at', '>=', $fromDateTime)->where('created_at', '<=', $toDateTime)->get();
        }else{
            $ticketsQuery = Resource::with('resourceInfo')

                ->where('created_at', '>=', $fromDateTime)
                ->where('created_at', '<=', $toDateTime);

                if ($social != null && $social != 'All') {
                    $ticketsQuery->whereHas('resourceInfo', function ($query) use ($social) {
                        $query->where('channel', $social);
                    });
                }

            $all_tickets = $ticketsQuery->get();
            
        }




        $received_tickets = $all_tickets->count();
        $new_tickets = $all_tickets->where('status', 'New')->count();
        $in_progress_tickets = $all_tickets->where('status', 'In Progress')->count();
        $reopen_tickets = $all_tickets->where('status', 'Reopen')->count();
        $pending_tickets = $all_tickets->where('status', 'Pending')->count();
        $resolved_tickets = $all_tickets->where('status', 'Resolved')->count();
        $closed_tickets = $all_tickets->where('status', 'Closed')->count();

        $fromDate = $fromDateTime;
        $toDate   = $toDateTime;

        if($filterType != 'voice'){
            $channel = $social;
            $channel = ($social !== '' && $social !== 'All') ? $social : null;

            $resources = Resource::with(['resourceInfo']);
            
            if ($channel != null) {
                $this->channel = $channel;
                $channel = Str::ucfirst(Str::lower($channel));
                $resources->whereHas('resourceInfo', function ($query) use ($channel) {
                    $query->where('channel', 'like', '%' . $channel . '%');
                });
            }else{
                $this->channel = null;
            }
            if ($fromDate != null) {

                $fromDate = Carbon::parse($fromDate)->startOfDay();
                $resources->where('created_at', '>=', $fromDate);
            } else {
                $fromDate = Carbon::today();
            }

            if ($toDate != null) {
                $toDate = Carbon::parse($toDate)->endOfDay();
            } else {
                $toDate = Carbon::today();
            }

            if ($fromDate != null && $toDate != null) {
                // Calculate the difference in days
                $dateDiff = (new DateTime($fromDate))->diff(new DateTime($toDate))->days;
            
                // Generate all hours of the day
                $allHours = $this->generateAllHours();
            
                // Generate all days between $fromDate and $toDate
                $allDays = $this->generateAllDays($fromDate, $toDate);
            
                // Generate all weeks between $fromDate and $toDate
                $allWeeks = $this->generateAllWeeks($fromDate, $toDate);
            
                // Generate all months between $fromDate and $toDate
                $allMonths = $this->generateAllMonths($fromDate, $toDate);
            
                // Determine the filter type based on the difference
                if ($dateDiff <= 1) {
                    // Filter type: day
                    $filteredData = $this->fillMissingHoursNonVoice($this->getTicketByDateNewNonVoice($fromDate), $allHours);
                    $filteredNoneData = $this->fillNoneMissingHoursNonVoice($this->getTicketByDateNoneNewNonVoice($fromDate), $allHours);
            
                } elseif ($dateDiff > 1 && $dateDiff <= 7) {
                    // Filter type: week
                    $filteredData = $this->fillMissingDaysNonVoice($this->getTicketByDateNewNonVoice($fromDate), $allDays);
                    $filteredNoneData = $this->fillNoneMissingDaysNonVoice($this->getTicketByDateNoneNewNonVoice($fromDate), $allDays);
                } elseif ($dateDiff > 7 && $dateDiff < 30) {
                    // Filter type: month
                    $filteredData = $this->fillMissingWeeksNonVoice($this->getTicketByDateNewNonVoice($fromDate), $allWeeks);
                    $filteredNoneData = $this->fillNoneMissingWeeksNonVoice($this->getTicketByDateNoneNewNonVoice($fromDate), $allWeeks);
                } else {
                    // Filter type: year
                    $filteredData = $this->fillMissingMonthsNonVoice($this->getTicketByDateNewNonVoice($fromDate), $allMonths);
                    $filteredNoneData = $this->fillNoneMissingMonthsNonVoice($this->getTicketByDateNoneNewNonVoice($fromDate), $allMonths);
                    
                }
            
                $ReceivedLabels = array_keys($filteredData);
                $ReceivedData = array_values($filteredData);

                // dd($ReceivedLabels,$ReceivedData);
            }
            

        }else{

            $resources = Ticket::with(['ticketInfo']);
            if ($fromDate != null) {

                $fromDate = Carbon::parse($fromDate)->startOfDay();
                $resources->where('created_at', '>=', $fromDate);
            } else {
                $fromDate = Carbon::today();
            }

            if ($toDate != null) {
                $toDate = Carbon::parse($toDate)->endOfDay();
            } else {
                $toDate = Carbon::today();
            }
            if ($fromDate != null && $toDate != null) {
                // Calculate the difference in days
                $dateDiff = (new DateTime($fromDate))->diff(new DateTime($toDate))->days;

                // Generate all hours of the day
                $allHours = $this->generateAllHours();

                // Generate all days between $fromDate and $toDate
                $allDays = $this->generateAllDays($fromDate, $toDate);

                // Generate all weeks between $fromDate and $toDate
                $allWeeks = $this->generateAllWeeks($fromDate, $toDate);

                // Generate all months between $fromDate and $toDate
                $allMonths = $this->generateAllMonths($fromDate, $toDate);

                // Determine the filter type based on the difference
                if ($dateDiff <= 1) {
                    // Filter type: day
                    $filteredData = $this->fillMissingHours($this->getTicketByDateNew($fromDate), $allHours);
                    $filteredNoneData = $this->fillNoneMissingHours($this->getTicketByDateNoneNew($fromDate), $allHours);
                } elseif ($dateDiff > 1 && $dateDiff <= 7) {
                    // Filter type: week
                    $filteredData = $this->fillMissingDays($this->getTicketByDateNew($fromDate), $allDays);
                    $filteredNoneData = $this->fillNoneMissingDays($this->getTicketByDateNoneNew($fromDate), $allDays);
                } elseif ($dateDiff > 7 && $dateDiff < 30) {
                    // Filter type: month
                    $filteredData = $this->fillMissingWeeks($this->getTicketByDateNew($fromDate), $allWeeks);
                    $filteredNoneData = $this->fillNoneMissingWeeks($this->getTicketByDateNoneNew($fromDate), $allWeeks);
                } else {
                    // Filter type: year
                    $filteredData = $this->fillMissingMonths($this->getTicketByDateNew($fromDate), $allMonths);
                    $filteredNoneData = $this->fillNoneMissingMonths($this->getTicketByDateNoneNew($fromDate), $allMonths);
                }
                $ReceivedLabels = array_keys($filteredData);
                $ReceivedData = array_values($filteredData);

            }
        }
        return view('tenant.supervisor.analytics.index', [
            'dateType' =>   $dateType,
            'filterType' =>   $filterType,
            'social' =>   $social,
            'fromDateTime' =>   $fromDateTime,
            'toDateTime' =>   $toDateTime,
            'avgAggregateTicketResolutionTime' =>   $avgAggregateTicketResolutionTime,
            'received_tickets' =>   $received_tickets,
            'new_tickets' =>   $new_tickets,
            'in_progress_tickets' =>   $in_progress_tickets,
            'reopen_tickets' =>   $reopen_tickets,
            'pending_tickets' =>   $pending_tickets,
            'resolved_tickets' =>   $resolved_tickets,
            'closed_tickets' =>   $closed_tickets,
        ], compact('ReceivedLabels', 'ReceivedData'));
    
    }
    private function getTicketByDateNew($date)
    {
        $records = Ticket::whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateNewRange($startDate, $endDate)
    {
        $records = Ticket::whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }
    private function getTicketByDateNoneNew($date)
    {
        $records = Ticket::where('status', '!=', 'New')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateNoneNewRange($startDate, $endDate)
    {
        $records = Ticket::where('status', '!=', 'New')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }
    private function getTicketByDateResolved($date)
    {
        $records = Ticket::where('status', 'Resolved')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateResolvedRange($startDate, $endDate)
    {
        $records = Ticket::where('status', 'Resolved')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }
    private function getTicketByDateClosed($date)
    {
        $records = Ticket::where('status', 'Closed')->whereDate('created_at', $date)

            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateClosedRange($startDate, $endDate)
    {
        $records = Ticket::where('status', 'Closed')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }

    private function getTicketByDateRedirected($date)
    {
        $records = Ticket::where('status', 'Redirect')->whereDate('created_at', $date)
            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateRedirectedRange($startDate, $endDate)
    {
        $records = Ticket::where('status', 'Redirect')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');
        return $records;
    }

    private function getTicketByDateReopen($date)
    {
        $records = Ticket::where('status', 'Reopen')->whereDate('created_at', $date)
            ->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');

        return $records;
    }
    private function getTicketByDateReopenRange($startDate, $endDate)
    {
        $records = Ticket::where('status', 'Reopen')->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');

        return $records;
    }
    private function fillMissingHours($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDays($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateNew($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeks($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateNewRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart)) . " | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonths($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateNewRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }
    private function fillNoneMissingHours($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillNoneMissingDays($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateNoneNew($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillNoneMissingWeeks($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateNoneNewRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart)) . " | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillNoneMissingMonths($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateNoneNewRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }
    private function fillMissingHoursResolved($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysResolved($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateResolved($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksResolved($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateResolvedRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart)) . " | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsResolved($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateResolvedRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }
    private function fillMissingHoursClosed($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysClosed($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateClosed($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksClosed($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateClosedRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart)) . " | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsClosed($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateClosedRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }
    private function fillMissingHoursRedirected($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysRedirected($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateRedirected($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksRedirected($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateRedirectedRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart)) . " | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsRedirected($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateRedirectedRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }
    private function fillMissingHoursReopen($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysReopen($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateReopen($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksReopen($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateReopenRange($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart)) . " | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsReopen($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateReopenRange($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }
    private function generateAllHours()
    {
        $result = array_map(function ($i) {
            return str_pad($i, 2, '0', STR_PAD_LEFT);
        }, range(0, 23));

        return $result;
    }
    private function generateAllDays($startDate, $endDate)
    {
        $result = [];
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $result[] = $currentDate;
            $currentDate = (new DateTime($currentDate))->modify('+1 day')->format('Y-m-d');
        }

        return $result;
    }
    private function generateAllWeeks($startDate, $endDate)
    {
        $result = [];
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $result[] = [$currentDate, (new DateTime($currentDate))->modify('+6 days')->format('Y-m-d')];
            $currentDate = (new DateTime($currentDate))->modify('+7 days')->format('Y-m-d');
        }

        return $result;
    }
    private function generateAllMonths($startDate, $endDate)
    {
        $result = [];
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $result[] = [$currentDate, (new DateTime($currentDate))->modify('last day of this month')->format('Y-m-d')];
            $currentDate = (new DateTime($currentDate))->modify('first day of next month')->format('Y-m-d');
        }

        return $result;
    }
  
    





































    private function getTicketByDateNewNonVoice($date)
    {
        $query = Resource::whereDate('created_at', $date);
        
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
        
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');
    
        return $records;
    }
    
    private function getTicketByDateNewRangeNonVoice($startDate, $endDate)
    {
        $query = Resource::whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');
    
        return $records;
    }
    
    private function getTicketByDateNoneNewNonVoice($date)
    {
        $query = Resource::where('status', '!=', 'New')
            ->whereDate('created_at', $date);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');
    
        return $records;
    }
    
    private function getTicketByDateNoneNewRangeNonVoice($startDate, $endDate)
    {
        $query = Resource::where('status', '!=', 'New')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');
    
        return $records;
    }
    
    private function getTicketByDateResolvedNonVoice($date)
    {
        $query = Resource::where('status', 'Resolved')
            ->whereDate('created_at', $date);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');
    
        return $records;
    }
    
    private function getTicketByDateResolvedRangeNonVoice($startDate, $endDate)
    {
        $query = Resource::where('status', 'Resolved')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');
    
        return $records;
    }
    
    private function getTicketByDateClosedNonVoice($date)
    {
        $query = Resource::where('status', 'Closed')
            ->whereDate('created_at', $date);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');
    
        return $records;
    }
    
    private function getTicketByDateClosedRangeNonVoice($startDate, $endDate)
    {
        $query = Resource::where('status', 'Closed')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');
    
        return $records;
    }
    
    private function getTicketByDateRedirectedNonVoice($date)
    {
        $query = Resource::where('status', 'Redirect')
            ->whereDate('created_at', $date);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');
    
        return $records;
    }
    
    private function getTicketByDateRedirectedRangeNonVoice($startDate, $endDate)
    {
        $query = Resource::where('status', 'Redirect')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');
    
        return $records;
    }
    
    private function getTicketByDateReopenNonVoice($date)
    {
        $query = Resource::where('status', 'Reopen')
            ->whereDate('created_at', $date);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%H") as hour, count(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour');
    
        return $records;
    }
    
    private function getTicketByDateReopenRangeNonVoice($startDate, $endDate)
    {
        $query = Resource::where('status', 'Reopen')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);
    
        if ($this->channel !== null) {
            $query->whereHas('resourceInfo', function ($query) {
                $query->where('channel', $this->channel);
            });
        }
    
        $records = $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d") as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date');
    
        return $records;
    }
    




















    private function fillMissingHoursNonVoice($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysNonVoice($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateNewNonVoice($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksNonVoice($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateNewRangeNonVoice($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsNonVoice($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateNewRangeNonVoice($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

    private function fillNoneMissingHoursNonVoice($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillNoneMissingDaysNonVoice($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateNoneNewNonVoice($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillNoneMissingWeeksNonVoice($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateNoneNewRangeNonVoice($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillNoneMissingMonthsNonVoice($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateNoneNewRangeNonVoice($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }


    private function fillMissingHoursResolvedNonVoice($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysResolvedNonVoice($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateResolvedNonVoice($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksResolvedNonVoice($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateResolvedRangeNonVoice($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsResolvedNonVoice($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateResolvedRangeNonVoice($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

    private function fillMissingHoursClosedNonVoice($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysClosedNonVoice($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateClosedNonVoice($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksClosedNonVoice($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateClosedRangeNonVoice($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsClosedNonVoice($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateClosedRangeNonVoice($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

    private function fillMissingHoursRedirectedNonVoice($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysRedirectedNonVoice($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateRedirectedNonVoice($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksRedirectedNonVoice($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateRedirectedRangeNonVoice($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsRedirectedNonVoice($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateRedirectedRangeNonVoice($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

    private function fillMissingHoursReopenNonVoice($data, $allHours)
    {
        $result = [];

        foreach ($allHours as $hour) {
            $result[$hour] = $data->get($hour, 0);
        }

        return $result;
    }
    private function fillMissingDaysReopenNonVoice($data, $allDays)
    {
        $result = [];

        foreach ($allDays as $day) {
            $dailyData = $this->getTicketByDateReopenNonVoice($day);
            $result[date('D', strtotime($day))] = array_sum($dailyData->toArray());
        }

        return $result;
    }
    private function fillMissingWeeksReopenNonVoice($data, $allWeeks)
    {
        $result = [];

        foreach ($allWeeks as [$weekStart, $weekEnd]) {
            $weekData = $this->getTicketByDateReopenRangeNonVoice($weekStart, $weekEnd);
            $result[date('Y-m-d', strtotime($weekStart))." | $weekEnd"] = array_sum($weekData->toArray());
        }

        return $result;
    }
    private function fillMissingMonthsReopenNonVoice($data, $allMonths)
    {
        $result = [];

        foreach ($allMonths as [$monthStart, $monthEnd]) {
            $monthData = $this->getTicketByDateReopenRangeNonVoice($monthStart, $monthEnd);
            $result[date('M', strtotime($monthStart))] = array_sum($monthData->toArray());
        }

        return $result;
    }

}
