@extends('tenant.layouts.supervisor')

{{-- Tilte Section --}}
@section('title', 'Analytics')

@section('filter')
    <div style="display: inline-block;">
        <!-- Icon box container -->
        {{-- <div id="iconBox" class="bg-white rounded shadow d-flex justify-content-between align-items-center position-absolute translate-middle-x z-index-1050 d-none"> --}}
        <div id="iconBox"
            class="d-flex justify-content-between align-items-center position-absolute translate-middle-x z-index-1050"
            style="margin: -13px -80px;">

            <a href="{{ route('supervisor.chatbotStatistics.index') }}" class="icons position-relative me-3"
                onclick="setActive(this)" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Chat Bot">
                <div class="arrow-top d-none" style="left: 7px;"><img src="{{ asset('img/menu_shape.png') }}" alt=""
                        width="10" style="transform: rotate(270deg);"></div>
                <img id='none-voice' src="{{ global_asset('tenancy/assets/img/chatbot_stats.png') }}" alt="mdo"
                    width="22" height="22">
            </a>
            <div class="icons position-relative me-3" onclick="setActive(this)" data-bs-toggle="tooltip"
                data-bs-placement="bottom" data-bs-title="Analytics">
                <div class="arrow-top" style="left: 7px;"><img src="{{ asset('img/menu_shape.png') }}" alt=""
                        width="10" style="transform: rotate(270deg);"></div>
                <img id='none-voice' src="{{ asset('img/analysis.png') }}" alt="mdo" width="22" height="22">
            </div>
            <a href="{{ route('supervisor.analytics.new') }}" class="icons position-relative me-3" onclick="setActive(this)"
                data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="New Analytics">
                <div class="arrow-top d-none" style="left: 7px;"><img src="{{ asset('img/menu_shape.png') }}" alt=""
                        width="10" style="transform: rotate(270deg);"></div>
                <img id='new-analytics' src="{{ asset('img/analysis.png') }}" alt="New Analytics" width="22"
                    height="22">
            </a>
        </div>

        <!-- Toggle Button -->
        {{-- <div class=" d-flex align-items-center" style="cursor:pointer" onclick="toggleIcons()" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Type">
            <div>
                <img class="text-success me-1" src="{{asset('img/arraow-dashboard-2.gif')}}" alt="" width="40px" height="40px">
            </div>             --}}
        {{-- <span class="text-success font-weight-medium">Types</span>
            <i class="fas fa-chevron-up text-success ms-1"></i> --}}
        {{-- </div> --}}
    </div>
@endsection

{{-- Style Section --}}
@section('style')

    <style>
        #iconBox {
            top: .6rem;
            left: 19rem;
            /* box-shadow: -0.2px 0.05rem 0.2rem 0px rgba(0, 0, 0, 0.15) !important; */
            padding: 20px 13px 6px;
        }

        .arrow-top {
            position: absolute;
            top: 29px;
            left: 8px;
        }

        .md-header {
            background: background: rgb(5, 177, 48);
            background: linear-gradient(90deg, rgba(5, 177, 48, 1) 0%, rgba(3, 153, 39, 1) 57%, rgba(195, 223, 0, 1) 100%);
            color: white;
        }

        .form-check-input:checked {
            background-color: #00a34e;
            border-color: #00a34e;
        }

        .form-check-input:checked+label {

            color: #00a34e;
        }

        .box-sta-filter {
            border: solid 2px;
            border-radius: 16px;
            padding: 5px;
            border-color: #d5d5d5;
            font-size: 10px;
            color: #818f87;
            width: 105px !important;
        }

        .filter-popup {
            display: block;
            width: 384px;
            max-height: 396px;
            overflow: initial;
            position: absolute;
            top: 198px;
            padding: 0;
            right: 357px;
            /* Changed from left: 0 */
            z-index: 999;
            background: white;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
    </style>

@endsection

{{-- Content Section --}}
@section('content')

    <div class="container-fluid">
        <div class="col-12">
            <div style="background: white !important;" class="card shadow  px-4 pe-0 py-3   rounded border border-0">
                <div class="card-body" style="position: relative">
                    <div class="row">
                        <div class="col-8">
                            <h5 class="card-title mb-2">Analytics</h5>

                            <p class="card-text text-secondary description">Integrate all the data and insights into your
                                workflow to create a more comprehensive of your customers.</p>
                        </div>
                        <div class="col-sm-2 col-2">
                            <button class="btn shadow-sm"
                                style="float: right ;display: inline-block; height: 44px;    margin-top: 7px;border-color: #e2e2e2;margin:0 0px"
                                onclick="refreshPage()" data-bs-toggle="tooltip" data-bs-placement="bottom"
                                data-bs-title="Refresh">
                                <img src="{{ asset('img/loading.png') }}" width="24" alt="">
                            </button>

                            <button data-bs-toggle="tooltip" data-bs-placement="bottom"
                                data-bs-custom-class="custom-tooltip" data-bs-title="Filter" onclick="showmod(event);"
                                data-bs-target="#filterModal" class="btn shadow-sm"
                                style="float: right ;display: inline-block; height: 44px;border-color: #e2e2e2; margin:0 18px">
                                <img src="{{ asset('img/Vector10.png') }}" width="24" alt="">
                            </button>

                        </div>
                        <div class="col-sm-2 col-2" style="width: 10% !important;">


                            <div class="dropdown custom-select" data-bs-toggle="tooltip" data-bs-placement="top"
                                data-bs-title="Filter by date">
                                <a class="btn btn-light dropdown-toggle select-toggle text-muted" onclick="toggleOptions2()"
                                    role="button" data-bs-toggle="dropdown" aria-expanded="false"
                                    style="background: white;border: 1px solid #e0e0e0;padding: 11px 13px;height: 44px;">
                                    {{ $dateType }}
                                </a>

                                <ul class="dropdown-menu select-options shadow-sm" id="mySelectOptions"
                                    style="padding: 8px">
                                    <li style="cursor: pointer" data-value="24 Hours" class="mb-2">Last 24 Hours</li>
                                    <li>
                                        <hr class="dropdown-divider mb-2">
                                    </li>
                                    <li style="cursor: pointer" data-value="7 Days" class="mb-2">Last 7 Days</li>
                                    <li>
                                        <hr class="dropdown-divider mb-2">
                                    </li>
                                    <li style="cursor: pointer" data-value="30 Days" class="mb-2">Last 30 Days</li>
                                    <li>
                                        <hr class="dropdown-divider mb-2">
                                    </li>
                                    <li style="cursor: pointer" data-value="30 Days" class="mb-2">Last 60 Days</li>
                                </ul>
                                <form action="/supervisor/analytics/filter" method="POST" id="myForm">
                                    @csrf
                                    <input type="hidden" id="mySelect2" name="mySelect2">
                                </form>
                            </div>

                        </div>
                    </div>

                    <div class="container filter-popup" id="filter"
                        style="width: 350px;display: none; background: none; border: none; top: 68px; padding: 0px; right: 21vw;;">
                        <!-- Your filter popup content goes here -->
                        <form action="/supervisor/analytics/filter" method="POST" onsubmit="validateForm(event)">
                            @csrf
                            <div class="row card rounded-4 shadow-sm">
                                <div class="modal-header md-header rounded-top-4"
                                    style="border-top-right-radius: 15px;height: 45px;border-top-left-radius: 15px;margin-top: 0;padding-top: 0;">
                                    <h1 class="modal-title fs-5" id="groupLevelModalLabel" style="margin-left: 15px;">
                                        Filter</h1>
                                    <button type="button" class="btn" data-bs-dismiss="modal" aria-label="Close"
                                        style="position: absolute;right: 0;" onclick="showmod(event);" id="x">
                                        <img src="{{ asset('img/remove.png') }}" class="shadow-sm" width="32"
                                            alt="">
                                    </button>
                                </div>
                                <div style="display: flex">
                                    <div class="col-sm-6 mt-3">
                                        <div class="col-sm-4 filter-pading mb-2">
                                            <script>
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    @if ($filterType == 'voice')
                                                        document.getElementById('voice').click();
                                                    @endif
                                                });
                                            </script>
                                            <label class="box-sta-filter filter-option-label1 text-center"
                                                style="{{ $filterType == 'voice' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" class="filter-input1" value="voice"
                                                    style="display: none;" name="type" id="voice"
                                                    onchange="toggleSubType()">
                                                <span></span>
                                                Voice
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 mt-3">
                                        <div class="col-sm-4 filter-pading mb-2">
                                            <label class="box-sta-filter filter-option-label1 text-center"
                                                style="{{ $filterType == 'non-voice' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" class="filter-input1" value="non-voice"
                                                    style="display: none;" name="type" id="non-voice"
                                                    onchange="toggleSubType()">
                                                <span></span>
                                                Non-Voice
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div id='subType' style="display: none">
                                    <div class="col-sm-12 mt-3 mb-3">
                                        Type
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4 filter-pading mb-2">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'Facebook' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" value="Facebook" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span><img id="facebook_img" class="mr-10"
                                                        src="{{ asset('img/<EMAIL>') }}" alt="mdo"
                                                        width="15" height="15"></span>
                                                Facebook
                                            </label>
                                        </div>
                                        <div class="col-sm-4 filter-pading mb-2">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'Whatsapp' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" class="filter-input" value="Whatsapp"
                                                    style="display: none;" name="social">
                                                <span>
                                                    <img id="col_img" class="mr-10"
                                                        src="{{ asset('img/whatsapp.png') }}" alt="mdo"
                                                        width="15" height="15">
                                                </span>
                                                Whatsapp
                                            </label>
                                        </div>
                                        <div class="col-sm-4 filter-pading mb-2">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'Email' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" value="Email" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span><img id="mail_img" class="mr-10"
                                                        src="{{ asset('img/mail.png') }}" alt="mdo" width="15"
                                                        height="15"></span>
                                                Email
                                            </label>
                                        </div>
                                        <div class="col-sm-4 filter-pading mb-2">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'Instagram' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" value="Instagram" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span><img id="instagram_img" class="mr-10"
                                                        src="{{ asset('img/instagram_color.png') }}" alt="mdo"
                                                        width="15" height="15"></span>
                                                Instagram
                                            </label>
                                        </div>

                                        <div class="col-sm-4 filter-pading mb-2">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'X' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" value="X" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span><img id="twitter_img" class="mr-10"
                                                        src="{{ asset('img/twitter_color.png') }}" alt="mdo"
                                                        width="15" height="15"></span>
                                                X
                                            </label>
                                        </div>

                                        <div class="col-sm-4 filter-pading">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'Youtube' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" value="Youtube" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span>
                                                    <i class="fa-brands fa-youtube fa-xl mr-10"
                                                        style="color: #02a34e;"></i>
                                                    Youtube
                                                </span>

                                            </label>
                                        </div>

                                        <div class="col-sm-4 filter-pading">

                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'Livechat' ? 'border-color: rgb(57, 179, 115);' : '' }}">

                                                <input type="radio" value="Livechat" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span><img id="webchat_img" class="mr-10"
                                                        src="{{ asset('img/web_chat_color.png') }}" alt="mdo"
                                                        width="15" height="15"></span>

                                                Livechat
                                            </label>
                                        </div>

                                        <div class="col-sm-4 filter-pading">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'Google' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" value="Google" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span>
                                                    <i class="fa-brands fa-google fa-xl mr-10"
                                                        style="color: #02a34e;"></i>
                                                    Google
                                                </span>

                                            </label>
                                        </div>
                                        <div class="col-sm-4 filter-pading">
                                            <label class="box-sta-filter filter-option-label"
                                                style="{{ $social == 'All' ? 'border-color: rgb(57, 179, 115);' : '' }}">
                                                <input type="radio" value="All" style="display: none;"
                                                    name="social" class="filter-input">
                                                <span>
                                                    <i class="fas fa-globe fa-xl mr-10" style="color: #02a34e;"></i>
                                                    All
                                                </span>

                                            </label>
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-12 mt-3">
                                    Date

                                </div>

                                <div class="row">
                                    <div class="col-6 mt-2">
                                        <label class="text-sm-start" style="font-size: 10px;">From date</label>
                                        <input type="date" class="form-control" id="From_date" name="From_date"
                                            style="font-size: 10px; border-radius: 0"
                                            value="{{ $fromDateTime ? $fromDateTime->format('Y-m-d') : '' }}">
                                        <div id="From_date_al" name="From_date_al"
                                            style="color: red; margin-left: 5px; font-size: 11px;"></div>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <label class="text-sm-start" style="font-size: 10px;">To date</label>
                                        <input type="date" class="form-control" id="To_date" name="To_date"
                                            style="font-size: 10px;border-radius: 0"
                                            value="{{ $toDateTime ? $toDateTime->format('Y-m-d') : '' }}">
                                        <div id="To_date_al" name="To_date_al"
                                            style="color: red; margin-left: 5px; font-size: 11px;"></div>
                                    </div>
                                </div>
                                <div class="col-12 mt-4 text-center mb-3">
                                    <button type="submit" class="btn btn-success align-content-center"
                                        style="background: #00a34e; color: white; width: 150px;">
                                        Show Result
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>

        <div class="col-12 px-3">
            <div class="row mb-1 py-4">
                <div class="col-lg-6 col-12 mt-2 mb-4 px-0 mx-0 padding-right-1">
                    <div class="card shadow     rounded border border-0"
                        style="margin-right: 33px;background: white !important;">
                        <div class="card-body">
                            <div class="row list-social mb-2"
                                style="height: 428px; overflow: scroll;  overflow: hidden !important; align-content: flex-start;">
                                <div class="row source-list mb-2" style="padding-left: 22px;height: fit-content">
                                    <h5 class="mb-3 py-2" style="text-align: left">Aggregate Ticket Resolution Time</h5>
                                    <hr class="head-seperator mb-4">
                                    <figure class="highcharts-figure">
                                        <canvas id="TicketResChartcontainer">
                                        </canvas>
                                    </figure>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-12 mt-2 mb-4 px-0 mx-0 padding-right-1">
                    <div style="background: white !important;" class="card shadow   rounded border border-0">
                        <div class="row ">
                            <div class="col-sm-12">
                                <div style="background: white !important;" class="card   rounded border border-0">
                                    <div class="card-body" style="height: 466px;">
                                        <div class="row">
                                            <h5 class="mb-3 py-2" style="text-align: left">Total Numbers Of Received
                                                Tickets is <strong
                                                    style="font-size: 30px;">{{ $received_tickets }}</strong></h5>
                                            <hr class="head-seperator mb-4">
                                            <div class="col-sm tim" id='cor33' style="padding: 24px 15px;">
                                                <div class="col-sm  float-left mr-5" style="display: inline;">
                                                    <span
                                                        style="border-radius: 51px; padding: 19px 14px; background-color: #f4f4f4;">
                                                        <img id='col_img3' style="padding-left: 5px;"
                                                            src="{{ asset('img/rec_ay.png') }}" alt="mdo"
                                                            width="31" height="28">
                                                    </span>
                                                </div>
                                                <div class="col-sm float-left mrt-5"
                                                    style="display: inline; margin-left: 36px;">
                                                    <strong style="font-size: 30px;">
                                                        {{ $new_tickets }}
                                                    </strong>
                                                    <br>
                                                    <div style="margin-left: 107px;">New Tickets</div>
                                                </div>
                                            </div>
                                            <div class="col-sm noti" id='cor4' style="padding: 18px 15px;">
                                                <div class="col-sm  float-left mr-5" style="display: inline;">
                                                    <span
                                                        style="border-radius: 51px; padding: 19px 14px; background-color: #f4f4f4;">
                                                        <img id='voice_img' style="border-radius: 51px;"
                                                            src="{{ asset('img/voice_ay.png') }}" alt="mdo"
                                                            width="31" height="28">
                                                    </span>
                                                </div>
                                                <div class="col-sm mrt-5" style="display: inline; margin-left: 36px;">
                                                    <strong style="font-size: 30px;">
                                                        {{ $in_progress_tickets }}
                                                    </strong>

                                                    <div style="margin-left: 107px;">In Progress Tickets</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm email" id='cor7' style="padding: 18px 15px;">
                                                <div class="col-sm  float-left mr-5" style="display: inline;">
                                                    <span
                                                        style="border-radius: 51px; padding: 19px 14px; background-color: #f4f4f4;">
                                                        <img id='email_img' style="padding-left: 5px;"
                                                            src="{{ asset('img/un_resolved_ay.png') }}" alt="mdo"
                                                            width="31" height="28">
                                                    </span>
                                                </div>
                                                <div class="col-sm mrt-5" style="display: inline;    margin-left: 36px;">
                                                    <strong style="font-size: 30px;">
                                                        {{ $reopen_tickets }}
                                                    </strong>
                                                    <br>
                                                    <div style="margin-left: 107px;">Reopen Tickets</div>
                                                </div>
                                            </div>
                                            <div class="col-sm cor" id='cor8' style="padding: 24px 15px;">
                                                <div class="col-sm  float-left mr-5 " style="display: inline;">
                                                    <span
                                                        style="border-radius: 51px; padding: 19px 14px; background-color: #f4f4f4;">
                                                        <img id='noti_img' style="padding-left: 5px;"
                                                            src="{{ asset('img/chat_ay.png') }}" alt="mdo"
                                                            width="31" height="28">
                                                    </span>
                                                </div>
                                                <div class="col-sm float-left mrt-5"
                                                    style="display: inline;    margin-left: 36px;">
                                                    <strong style="font-size: 30px;">{{ $pending_tickets }}</strong>
                                                    <br>
                                                    <div style="margin-left: 107px;">Pending Tickets</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm tim" id='cor5' style="padding: 18px 15px;">
                                                <div class="col-sm  float-left mr-5" style="display: inline;">
                                                    <span
                                                        style="border-radius: 51px; padding: 19px 14px; background-color: #f4f4f4;">
                                                        <img id='tim_img' style="padding-left: 5px;"
                                                            src="{{ asset('img/resolved_ay.png') }}" alt="mdo"
                                                            width="31" height="28">
                                                    </span>
                                                </div>
                                                <div class="col-sm float-left mrt-5"
                                                    style="display: inline;    margin-left: 36px;">
                                                    <strong style="font-size: 30px;">
                                                        {{ $resolved_tickets }}
                                                    </strong>
                                                    <br>
                                                    <div style="margin-left: 107px;">Resolved Tickets</div>
                                                </div>
                                            </div>
                                            <div class="col-sm over" id='cor6' style="padding: 18px 15px;">
                                                <div class="col-sm  float-left mr-5" style="display: inline;">
                                                    <span
                                                        style="border-radius: 51px; padding: 19px 14px; background-color: #f4f4f4;">
                                                        <img id='over_img' style="padding-left: 5px;"
                                                            src="{{ asset('img/rec_ay.png') }}" alt="mdo"
                                                            width="31" height="28">
                                                    </span>
                                                </div>
                                                <div class="col-sm mrt-5" style="display: inline;    margin-left: 36px;">
                                                    <strong style="font-size: 30px;">
                                                        {{ $closed_tickets }}
                                                    </strong>
                                                    <br>
                                                    <div style="margin-left: 107px;">Closed Tickets</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="background: white !important;" class="card shadow    rounded border border-0 mt-3">
                    <div class="card-body" style="padding-left: 34px;">

                        <h4 style="    margin-bottom: 50px;">
                            Received Trend
                            <span
                                style="font-size: 17px;    margin-right: 28px;
                            float: right;">Received</span>
                            <span
                                style="     display: block;
                                width: 22px !important;
                                height: 22px;
                                float: right;
                                margin-right: 13px;
                                border: 1px solid green;
                                box-sizing: border-box;
                                background-color: green;">
                            </span>
                            <hr class="head-seperator mt-3">
                            </h3>
                            <canvas id="RecivedticketsChart" style="height: 370px; width: 100%;"></canvas>
                    </div>
                </div>

            </div>
        </div>
    </div>


@endsection

{{-- Script Section --}}
@section('script')
    <script src="https://cdn.canvasjs.com/canvasjs.min.js"></script>
    <script src="https://cdn.canvasjs.com/canvasjs.min.js"></script>
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script type="text/javascript" src="https://canvasjs.com/assets/script/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="https://canvasjs.com/assets/script/jquery.canvasjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // function toggleIcons() {
        //      const iconBox = document.getElementById('iconBox');
        //      iconBox.classList.toggle('d-none');
        //  }

        function setActive(selected) {
            document.querySelectorAll('.icons').forEach(icon => {
                icon.classList.remove('active');
                icon.querySelector('.arrow-top').classList.add('d-none');
            });

            selected.classList.add('active');
            selected.querySelector('.arrow-top').classList.remove('d-none');
        }
    </script>
    <script>
        function toggleFilterPopup() {
            var filterPopup = document.getElementById('filter');
            filterPopup.style.display = filterPopup.style.display === 'none' ? 'block' : 'none';
        }
    </script>
    {{-- OnChange Date Filter --}}
    <script>
        function toggleOptions2() {
            const selectOptions = document.getElementById('mySelectOptions');
        }
        const options2 = document.querySelectorAll('.select-options li');
        options2.forEach((optionSA) => {
            optionSA.addEventListener('click', () => {
                const selectedValue = optionSA.dataset.value;
                const selectToggle = document.querySelector('.select-toggle');
                const selectOptions = document.getElementById('mySelectOptions');
                const mySelectInput = document.getElementById('mySelect2');

                selectToggle.textContent = selectedValue;
                selectOptions.style.display = 'none';
                mySelectInput.value = selectedValue;

                // Submit the form when an option is selected
                document.getElementById('myForm').submit();
            });
        });
    </script>
    {{-- Aggregate Ticket Resolution Time Chart --}}
    <script>
        const colors = {
            gray: {
                default: "rgba(211,211,211,1)",
                half: "rgba(211,211,211,0.44021358543417366)",
                quarter: "rgba(211,211,211,0.16010154061624648)",
                zero: "rgba(211,211,211,0)"
            },
            indigo: {
                default: "rgba(80, 102, 120, 1)",
                quarter: "rgba(80, 102, 120, 0.25)"
            },
            black: {
                default: " rgba(0,0,0,0.1)",
                half: "rgba(0, 0, 0, 44021358543417366)",
                quarter: "rgba(0, 0, 0, 0.16010154061624648)",
                zero: "rgba(0, 0, 0, 0)"
            },
            yellowToGreen: {
                default: "rgba(187,207,52,1)",
                half: "rgba(137,218,55,44021358543417366)",
                quarter: "rgba(26,212,35, 0.16010154061624648)",
                zero: " rgba(18,171,82,0)"
            },
            yellowToGreenBorder: {
                default: "rgba(187,207,52,0.1)",
                half: "rgba(137,218,55,0.44021358543417366)",
                quarter: "rgba(26,212,35, 0.55021358543417366)",
                zero: " rgba(18,171,82,1)"
            }
        };

        const ctxresT = document.getElementById('TicketResChartcontainer').getContext("2d");
        const dataresT = {
            labels: [
                '   Average resolution time',
                '   Resolution within SLA',
                '   Reopened tickets'
            ],
            datasets: [{

                    data: [
                        0, 50
                    ],
                    backgroundColor: ['#00a34e', '#d9f1e5'],
                    borderWidth: 0,
                    hoverOffset: 10,
                    cutout: 130,
                },
                {

                    data: [100],
                    backgroundColor: '#00a34e',
                    borderWidth: 0,
                    hoverOffset: 10,
                    cutout: 90,
                }
            ],
        };
        const optionsresT = {
            hover: {
                mode: null
            },
            responsive: true,
            aspectRatio: 1,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    enabled: false
                },
                legend: {
                    fullSize: true,
                    display: false,
                    position: 'bottom',
                    align: 'start',
                    labels: {
                        color: 'gray',
                        font: {
                            weight: 'bold'
                        },
                        boxWidth: 15,
                        boxHeight: 15,
                        padding: 25
                    }
                }
            },
            elements: {
                center: {
                    color: '#bbcf34', // Default is #000000
                    sidePadding: 20, // Default is 20 (as a percentage)
                    minFontSize: 25, // Default is 20 (in px), set to false and text will not wrap.
                    lineHeight: 25 // Default is 25 (in px), used for when text wraps
                }
            }

        };
        const configresT = {
            type: 'doughnut',
            data: dataresT,
            options: optionsresT,
            radius: 1
        };
        new Chart(ctxresT, configresT, optionsresT);
        textCenter('{{ $avgAggregateTicketResolutionTime }}', 'TicketResChartcontainer');

        function textCenter(val, containerName) {
            Chart.register({
                id: 'myGraphText',
                beforeDraw: function(chart) {
                    if (chart.canvas.id != containerName) {
                        return 0;
                    }
                    var width = chart.width,
                        height = chart.height,
                        ctx = chart.ctx;

                    ctx.restore();
                    var fontSize = (height / 150).toFixed(2);
                    ctx.font = fontSize + "em sans-serif";
                    ctx.textBaseline = "middle";

                    var text = val,
                        textX = Math.round((width - ctx.measureText(text).width) / 2),
                        textY = height / 2;

                    ctx.fillText(text, textX, textY);
                    ctx.save();
                }
            });
        }
    </script>

    <script>
        function refreshPage() {
            // Send a POST request to the controller route
            fetch('/supervisor/analytics/filter', {
                    method: 'POST',
                    body: JSON.stringify({
                        data: '30'
                    }),
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (response.status === 419) {
                        // Handle session expiration or invalid session here
                        // For example, you can redirect the user to the login page
                        window.location.href = '/supervisor/analytics';
                    } else {
                        // Handle other response statuses if needed
                        handleSelectChange();
                    }
                })
                .catch(error => {
                    // Handle any error that occurred during the request
                });

            // Optionally, you can redirect the user to the controller route
            // window.location.href = '/supervisor/analytics/filter';
        }
    </script>

    <script>
        $(document).ready(function() {
            var elements = [{
                    element: $('#cor33'),
                    image: $('#col_img3'),
                    normalImage: '/tenancy/assets/img/rec_ay.png',
                    hoverImage: '/tenancy/assets/img/rec_ay_c.png'
                },
                {
                    element: $('#cor4'),
                    image: $('#voice_img'),
                    normalImage: '/tenancy/assets/img/voice_ay.png',
                    hoverImage: '/tenancy/assets/img/voice_ay_c.png'
                },
                {
                    element: $('#cor5'),
                    image: $('#tim_img'),
                    normalImage: '/tenancy/assets/img/resolved_ay.png',
                    hoverImage: '/tenancy/assets/img/resol_ay_c.png'
                },
                {
                    element: $('#cor6'),
                    image: $('#over_img'),
                    normalImage: '/tenancy/assets/img/rec_ay.png',
                    hoverImage: '/tenancy/assets/img/rec_ay_c.png'
                },
                {
                    element: $('#cor7'),
                    image: $('#email_img'),
                    normalImage: '/tenancy/assets/img/un_resolved_ay.png',
                    hoverImage: '/tenancy/assets/img/unresol_ay_c.png'
                },
                {
                    element: $('#cor8'),
                    image: $('#noti_img'),
                    normalImage: '/tenancy/assets/img/chat_ay.png',
                    hoverImage: '/tenancy/assets/img/chat_ay_c.png'
                }
            ];

            elements.forEach(function(elementData) {
                var element = elementData.element;
                var image = elementData.image;
                var normalImage = elementData.normalImage;
                var hoverImage = elementData.hoverImage;
                var originalPosition = element.css('transform');
                var hoverPosition = 'translate(-2px, -2px)';

                element.mouseover(function() {
                    image.attr('src', hoverImage);
                    element.css('background-color', '#ebebeb');
                    element.css('transition', 'all .2s linear');
                    element.css('transform',
                        hoverPosition); // Move element to a slightly less top position
                });

                element.mouseout(function() {
                    image.attr('src', normalImage);
                    element.css('background-color', '#ffffff');
                    element.css('transition', 'all .1s linear');
                    element.css('transform', originalPosition); // Reset the position
                });
            });
        });
    </script>

    <script type="text/javascript">
        const recivedCtx = document.getElementById('RecivedticketsChart').getContext("2d");
        const yellowToGreenGradient = recivedCtx.createLinearGradient(0, 25, 0, 300);
        yellowToGreenGradient.addColorStop(0, colors.yellowToGreen.half);
        yellowToGreenGradient.addColorStop(0.35, colors.yellowToGreen.quarter);
        yellowToGreenGradient.addColorStop(1, colors.yellowToGreen.zero);
        const yellowToGreenBorderGradient = recivedCtx.createLinearGradient(0, 25, 0, 300);
        yellowToGreenBorderGradient.addColorStop(0, colors.yellowToGreenBorder.half);
        yellowToGreenBorderGradient.addColorStop(0.35, colors.yellowToGreenBorder.quarter);
        yellowToGreenBorderGradient.addColorStop(1, colors.yellowToGreenBorder.zero);
        const labelsNew = <?php echo json_encode($ReceivedLabels); ?>;
        const dataNew = <?php echo json_encode($ReceivedData); ?>;

        const Redata = {
            labels: labelsNew,
            datasets: [{
                backgroundColor: yellowToGreenGradient,
                label: 'Trend',
                data: dataNew,
                borderColor: yellowToGreenBorderGradient,
                fill: true,
                tension: 0.4,
                pointRadius: 0,
            }, ],
        };

        const Recconfig = {
            type: 'line',
            data: Redata,
            options: {
                responsive: true,
                scales: {
                    x: {
                        stacked: false,
                        grid: {
                            display: false,
                        },
                        gridLines: {
                            drawBorder: false,
                        },
                        ticks: {
                            color: '#858585',
                        },
                    },
                    y: {
                        stacked: false,
                        gridLines: {
                            drawBorder: false,
                        },
                        ticks: {
                            color: '#858585',
                            textStrokeColor: 'white',
                        },
                        border: {
                            display: false,
                        },
                    },
                },
                plugins: {
                    filler: {
                        propagate: false,
                    },
                    title: {
                        display: false,
                        text: 'Hello',
                    },
                    legend: {
                        fullSize: true,
                        display: true,
                        position: 'top',
                        align: 'end',
                        padding: 10,
                        color: '#282828',
                        background: '#282828',
                        labels: {
                            color: '#282828',
                            font: {
                                weight: 'bold',
                                padding: 20,
                            },
                            boxWidth: 15,
                            boxHeight: 15,
                            maxHeight: 50,
                        },
                        title: {},
                    },
                },
                interaction: {
                    intersect: false,
                },
            },
        };

        new Chart(recivedCtx, Recconfig);
    </script>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var selectElement = document.getElementById('mySelect');
            selectElement.addEventListener('change', function() {
                var selectedOption = selectElement.value;
                if (selectedOption ===
                    'option2') { // Specify the option value for which you want to show the modal
                    var modalElement = document.getElementById('emailModal');
                    modalElement.classList.add('show');
                    modalElement.style.display = 'block';
                }
            });

            var closeButton = document.querySelector('.btn-close');
            closeButton.addEventListener('click', function() {
                var modalElement = document.getElementById('emailModal');
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
            });
        });


        function validateForm(event) {
            event.preventDefault(); // Prevent form submission

            var inputs = document.querySelectorAll('#filter input'); // Get all input elements inside the form
            var isValid = true;

            inputs.forEach(function(input) {
                if (input.value.trim() === '') {
                    isValid = false;
                    input.classList.add('error'); // Add a CSS class to highlight the empty input
                } else {
                    input.classList.remove('error'); // Remove the CSS class if the input is not empty
                }
            });

            if (!isValid) {
                var fr_d = document.getElementById("From_date").value;
                var to_d = document.getElementById("To_date").value;
                if (fr_d == "") {
                    document.getElementById("From_date_al").innerHTML = "this field can't be empty."
                } else {
                    document.getElementById("From_date_al").innerHTML = ""
                }
                if (to_d == "") {
                    document.getElementById("To_date_al").innerHTML = "this field can't be empty."
                } else {
                    document.getElementById("To_date_al").innerHTML = ""
                }
            } else {
                event.target.submit(); // Submit the form if all inputs are filled
            }
        }

        $('.filter-input').click((event) => {
            $('.filter-option-label').css({
                "border-color": "#d5d5d5"
            });
            console.log($(event.target).parent().css({
                "border-color": "#39b373"
            }));
        })
        $('.filter-input1').click((event) => {
            $('.filter-option-label1').css({
                "border-color": "#d5d5d5"
            });
            console.log($(event.target).parent().css({
                "border-color": "#39b373"
            }));
        })

        function showmod(event) {
            event.stopPropagation(); // Prevent event propagation

            var modalContent = document.getElementById("filter");

            if (modalContent) {
                if (modalContent.style.display === "none" || modalContent.style.display === "") {
                    modalContent.style.display = "block";
                } else {
                    modalContent.style.display = "none";
                }
            } else {
                console.log("Element with ID 'filter' not found.");
            }
        }

        document.addEventListener("click", function(event) {
            var modalContent = document.getElementById("filter");
            var targetElement = event.target;

            if (modalContent && targetElement !== modalContent && !modalContent.contains(targetElement) &&
                targetElement.className !== "icons-2") {
                setTimeout(function() {
                    modalContent.style.display = "none";
                }, 100);
            }
        });


        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
    </script>
    <script>
        function toggleSubType() {
            const subTypeDiv = document.getElementById('subType');
            const voiceRadio = document.getElementById('voice');

            if (voiceRadio.checked) {
                subTypeDiv.style.display = 'none';
            } else {
                subTypeDiv.style.display = 'block';
            }
        }
    </script>
@endsection
