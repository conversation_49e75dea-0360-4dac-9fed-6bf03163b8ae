<?php

namespace App\Http\Livewire\Tenant\Supervisor\Analytics;

use App\Models\Tenant\Resource;
use App\Models\Tenant\Ticket;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Str;
use Livewire\Component;

class NewAnalytics extends Component
{
    // Filter Properties - Exact same as old analytics
    public $selectedDateFilter = '30 Days';
    public $selectedChannel = '';
    public $fromDate = '';
    public $toDate = '';
    public $showFilterPopup = false;

    // Filter Methods - Exact same functionality as old analytics
    public function updatedSelectedDateFilter()
    {
        $this->resetCustomDates();
    }

    public function updatedSelectedChannel()
    {
        // Channel filter changed
    }

    public function updatedFromDate()
    {
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
    }

    public function updatedToDate()
    {
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
    }

    public function toggleFilterPopup()
    {
        $this->showFilterPopup = !$this->showFilterPopup;
    }

    public function resetFilters()
    {
        $this->selectedDateFilter = '30 Days';
        $this->selectedChannel = '';
        $this->fromDate = '';
        $this->toDate = '';
        $this->showFilterPopup = false;
    }

    private function resetCustomDates()
    {
        $this->fromDate = '';
        $this->toDate = '';
    }

    public function applyFilters()
    {
        $this->showFilterPopup = false;
    }

    public function render()
    {
        return view('livewire.tenant.supervisor.analytics.new-analytics');
    }
}
